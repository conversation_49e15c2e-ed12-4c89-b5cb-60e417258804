"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getEmployeeStats = exports.deleteEmployee = exports.updateEmployee = exports.createEmployee = exports.getEmployeeById = exports.getEmployees = void 0;
const database_1 = require("../config/database");
// Get all employees with filtering and pagination
const getEmployees = async (req, res) => {
    try {
        const { department_id, employment_status, employment_type, position, search, page = 1, limit = 10, sort_by = 'created_at', sort_order = 'DESC' } = req.query;
        // Validate sort parameters to prevent SQL injection
        const validSortColumns = ['created_at', 'first_name', 'last_name', 'employee_id', 'position', 'employment_status'];
        const validSortOrder = ['ASC', 'DESC'];
        const safeSortBy = validSortColumns.includes(sort_by) ? sort_by : 'created_at';
        const safeSortOrder = validSortOrder.includes(sort_order) ? sort_order : 'DESC';
        const offset = (Number(page) - 1) * Number(limit);
        const conditions = [];
        const params = [];
        // Build WHERE conditions
        if (department_id) {
            conditions.push('e.department_id = ?');
            params.push(department_id);
        }
        if (employment_status) {
            conditions.push('e.employment_status = ?');
            params.push(employment_status);
        }
        if (employment_type) {
            conditions.push('e.employment_type = ?');
            params.push(employment_type);
        }
        if (position) {
            conditions.push('e.position LIKE ?');
            params.push(`%${position}%`);
        }
        if (search) {
            conditions.push(`(
        e.first_name LIKE ? OR 
        e.last_name LIKE ? OR 
        e.employee_id LIKE ? OR 
        e.email LIKE ? OR
        CONCAT(e.first_name, ' ', e.last_name) LIKE ?
      )`);
            const searchTerm = `%${search}%`;
            params.push(searchTerm, searchTerm, searchTerm, searchTerm, searchTerm);
        }
        const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
        // Get total count
        const countQuery = `
      SELECT COUNT(*) as total
      FROM employees e
      JOIN departments d ON e.department_id = d.id
      ${whereClause}
    `;
        const countResult = await (0, database_1.executeQuery)(countQuery, params);
        const total = countResult[0].total;
        // Get employees with department info
        const employeesQuery = `
      SELECT 
        e.*,
        d.name as department_name,
        d.code as department_code,
        CONCAT(eh.first_name, ' ', eh.last_name) as department_head_name
      FROM employees e
      JOIN departments d ON e.department_id = d.id
      LEFT JOIN employees eh ON d.department_head_id = eh.id
      ${whereClause}
      ORDER BY e.${safeSortBy} ${safeSortOrder}
      LIMIT ? OFFSET ?
    `;
        // Create parameters array for the employees query
        // Include all WHERE clause parameters plus LIMIT and OFFSET
        const employeesParams = [...params, Number(limit), offset];
        console.log('Query:', employeesQuery);
        console.log('Params:', employeesParams);
        console.log('Params count:', employeesParams.length);
        console.log('Placeholders count:', (employeesQuery.match(/\?/g) || []).length);
        const employees = await (0, database_1.executeQuery)(employeesQuery, employeesParams);
        const response = {
            employees: employees,
            total,
            page: Number(page),
            limit: Number(limit),
            total_pages: Math.ceil(total / Number(limit))
        };
        res.json(response);
    }
    catch (error) {
        console.error('Get employees error:', error);
        res.status(500).json({ error: 'Failed to get employees' });
    }
};
exports.getEmployees = getEmployees;
// Get employee by ID
const getEmployeeById = async (req, res) => {
    try {
        const { id } = req.params;
        const employees = await (0, database_1.executeQuery)(`
      SELECT 
        e.*,
        d.name as department_name,
        d.code as department_code,
        CONCAT(eh.first_name, ' ', eh.last_name) as department_head_name,
        u.username, u.email as user_email, u.role as user_role
      FROM employees e
      JOIN departments d ON e.department_id = d.id
      LEFT JOIN employees eh ON d.department_head_id = eh.id
      LEFT JOIN users u ON e.user_id = u.id
      WHERE e.id = ?
    `, [id]);
        if (!employees || employees.length === 0) {
            res.status(404).json({ error: 'Employee not found' });
            return;
        }
        res.json({ employee: employees[0] });
    }
    catch (error) {
        console.error('Get employee error:', error);
        res.status(500).json({ error: 'Failed to get employee' });
    }
};
exports.getEmployeeById = getEmployeeById;
// Create new employee
const createEmployee = async (req, res) => {
    try {
        const employeeData = req.body;
        // Check if employee_id already exists
        const existingEmployees = await (0, database_1.executeQuery)('SELECT id FROM employees WHERE employee_id = ?', [employeeData.employee_id]);
        if (existingEmployees && existingEmployees.length > 0) {
            res.status(409).json({ error: 'Employee ID already exists' });
            return;
        }
        // Check if department exists
        const departments = await (0, database_1.executeQuery)('SELECT id FROM departments WHERE id = ? AND is_active = TRUE', [employeeData.department_id]);
        if (!departments || departments.length === 0) {
            res.status(400).json({ error: 'Invalid department ID' });
            return;
        }
        // Check if user_id exists and is not already assigned
        if (employeeData.user_id) {
            const users = await (0, database_1.executeQuery)('SELECT id FROM users WHERE id = ?', [employeeData.user_id]);
            if (!users || users.length === 0) {
                res.status(400).json({ error: 'Invalid user ID' });
                return;
            }
            const existingUserEmployees = await (0, database_1.executeQuery)('SELECT id FROM employees WHERE user_id = ?', [employeeData.user_id]);
            if (existingUserEmployees && existingUserEmployees.length > 0) {
                res.status(409).json({ error: 'User is already assigned to another employee' });
                return;
            }
        }
        // Insert new employee
        const insertQuery = `
      INSERT INTO employees (
        employee_id, user_id, first_name, last_name, middle_name, date_of_birth, gender, marital_status, nationality,
        email, phone, emergency_contact_name, emergency_contact_phone, emergency_contact_relationship,
        address_line1, address_line2, city, state, postal_code, country,
        department_id, position, employment_type, employment_status, hire_date, termination_date, salary, hourly_rate
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
        const insertParams = [
            employeeData.employee_id,
            employeeData.user_id || null,
            employeeData.first_name,
            employeeData.last_name,
            employeeData.middle_name || null,
            employeeData.date_of_birth || null,
            employeeData.gender || null,
            employeeData.marital_status || null,
            employeeData.nationality || null,
            employeeData.email || null,
            employeeData.phone || null,
            employeeData.emergency_contact_name || null,
            employeeData.emergency_contact_phone || null,
            employeeData.emergency_contact_relationship || null,
            employeeData.address_line1 || null,
            employeeData.address_line2 || null,
            employeeData.city || null,
            employeeData.state || null,
            employeeData.postal_code || null,
            employeeData.country || 'Indonesia',
            employeeData.department_id,
            employeeData.position,
            employeeData.employment_type,
            employeeData.employment_status || 'active',
            employeeData.hire_date,
            employeeData.termination_date || null,
            employeeData.salary || null,
            employeeData.hourly_rate || null
        ];
        const result = await (0, database_1.executeQuery)(insertQuery, insertParams);
        // Get the created employee with department info
        const newEmployees = await (0, database_1.executeQuery)(`
      SELECT 
        e.*,
        d.name as department_name,
        d.code as department_code
      FROM employees e
      JOIN departments d ON e.department_id = d.id
      WHERE e.id = ?
    `, [result.insertId]);
        res.status(201).json({
            message: 'Employee created successfully',
            employee: newEmployees[0]
        });
    }
    catch (error) {
        console.error('Create employee error:', error);
        res.status(500).json({ error: 'Failed to create employee' });
    }
};
exports.createEmployee = createEmployee;
// Update employee
const updateEmployee = async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = req.body;
        // Check if employee exists
        const existingEmployees = await (0, database_1.executeQuery)('SELECT id, employee_id, user_id FROM employees WHERE id = ?', [id]);
        if (!existingEmployees || existingEmployees.length === 0) {
            res.status(404).json({ error: 'Employee not found' });
            return;
        }
        const existingEmployee = existingEmployees[0];
        // Check if employee_id is being changed and if it already exists
        if (updateData.employee_id && updateData.employee_id !== existingEmployee.employee_id) {
            const duplicateEmployees = await (0, database_1.executeQuery)('SELECT id FROM employees WHERE employee_id = ? AND id != ?', [updateData.employee_id, id]);
            if (duplicateEmployees && duplicateEmployees.length > 0) {
                res.status(409).json({ error: 'Employee ID already exists' });
                return;
            }
        }
        // Check if department exists
        if (updateData.department_id) {
            const departments = await (0, database_1.executeQuery)('SELECT id FROM departments WHERE id = ? AND is_active = TRUE', [updateData.department_id]);
            if (!departments || departments.length === 0) {
                res.status(400).json({ error: 'Invalid department ID' });
                return;
            }
        }
        // Build update query
        const updates = [];
        const values = [];
        Object.keys(updateData).forEach(key => {
            if (updateData[key] !== undefined) {
                updates.push(`${key} = ?`);
                values.push(updateData[key]);
            }
        });
        if (updates.length === 0) {
            res.status(400).json({ error: 'No valid fields to update' });
            return;
        }
        updates.push('updated_at = CURRENT_TIMESTAMP');
        values.push(id);
        await (0, database_1.executeQuery)(`UPDATE employees SET ${updates.join(', ')} WHERE id = ?`, values);
        // Get updated employee
        const updatedEmployees = await (0, database_1.executeQuery)(`
      SELECT 
        e.*,
        d.name as department_name,
        d.code as department_code
      FROM employees e
      JOIN departments d ON e.department_id = d.id
      WHERE e.id = ?
    `, [id]);
        res.json({
            message: 'Employee updated successfully',
            employee: updatedEmployees[0]
        });
    }
    catch (error) {
        console.error('Update employee error:', error);
        res.status(500).json({ error: 'Failed to update employee' });
    }
};
exports.updateEmployee = updateEmployee;
// Delete employee (soft delete by changing status)
const deleteEmployee = async (req, res) => {
    try {
        const { id } = req.params;
        // Check if employee exists
        const employees = await (0, database_1.executeQuery)('SELECT id, employment_status FROM employees WHERE id = ?', [id]);
        if (!employees || employees.length === 0) {
            res.status(404).json({ error: 'Employee not found' });
            return;
        }
        // Soft delete by updating status
        await (0, database_1.executeQuery)('UPDATE employees SET employment_status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?', ['terminated', id]);
        res.json({ message: 'Employee deleted successfully' });
    }
    catch (error) {
        console.error('Delete employee error:', error);
        res.status(500).json({ error: 'Failed to delete employee' });
    }
};
exports.deleteEmployee = deleteEmployee;
// Get employee statistics
const getEmployeeStats = async (req, res) => {
    try {
        const stats = await (0, database_1.executeQuery)(`
      SELECT 
        COUNT(*) as total_employees,
        COUNT(CASE WHEN employment_status = 'active' THEN 1 END) as active_employees,
        COUNT(CASE WHEN employment_status = 'inactive' THEN 1 END) as inactive_employees,
        COUNT(CASE WHEN employment_status = 'terminated' THEN 1 END) as terminated_employees,
        COUNT(CASE WHEN employment_type = 'full_time' THEN 1 END) as full_time_employees,
        COUNT(CASE WHEN employment_type = 'part_time' THEN 1 END) as part_time_employees,
        COUNT(CASE WHEN employment_type = 'contract' THEN 1 END) as contract_employees,
        COUNT(CASE WHEN employment_type = 'intern' THEN 1 END) as intern_employees,
        AVG(salary) as average_salary,
        COUNT(CASE WHEN YEAR(hire_date) = YEAR(CURRENT_DATE()) THEN 1 END) as new_hires_this_year
      FROM employees
    `);
        res.json({ stats: stats[0] });
    }
    catch (error) {
        console.error('Get employee stats error:', error);
        res.status(500).json({ error: 'Failed to get employee statistics' });
    }
};
exports.getEmployeeStats = getEmployeeStats;
//# sourceMappingURL=employeeController.js.map