'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { withAuth } from '@/hooks/useAuth';
import DashboardLayout from '@/components/layout/DashboardLayout';
import EmployeeForm from '@/components/forms/EmployeeForm';
import { CreateEmployeeRequest } from '@/types';
import { apiService } from '@/services/api';

function NewEmployeePage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (data: CreateEmployeeRequest) => {
    try {
      setLoading(true);
      await apiService.createEmployee(data);
      
      // Show success message (you can implement a toast system)
      alert('Employee created successfully!');
      
      // Redirect to employees list
      router.push('/employees');
    } catch (error: any) {
      console.error('Failed to create employee:', error);
      
      // Show error message
      const errorMessage = error.response?.data?.error || 'Failed to create employee';
      alert(errorMessage);
      
      // Re-throw to let form handle validation errors
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    router.push('/employees');
  };

  return (
    <DashboardLayout title="Add New Employee">
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Add New Employee</h1>
          <p className="text-gray-600">Create a new employee record in the system</p>
        </div>

        <EmployeeForm
          mode="create"
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          loading={loading}
        />
      </div>
    </DashboardLayout>
  );
}

export default withAuth(NewEmployeePage, ['admin', 'hr']);
