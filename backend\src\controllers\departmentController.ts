import { Request, Response } from 'express';
import { executeQuery } from '../config/database';
import { 
  Department, 
  DepartmentWithStats, 
  CreateDepartmentRequest, 
  UpdateDepartmentRequest,
  DepartmentSearchFilters,
  DepartmentListResponse 
} from '../models/Department';

// Get all departments with filtering and pagination
export const getDepartments = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      is_active,
      search,
      page = 1,
      limit = 10,
      sort_by = 'name',
      sort_order = 'ASC'
    }: DepartmentSearchFilters = req.query;

    const offset = (Number(page) - 1) * Number(limit);
    const conditions: string[] = [];
    const params: any[] = [];

    // Build WHERE conditions
    if (is_active !== undefined) {
      conditions.push('d.is_active = ?');
      const isActiveValue = `${is_active}` === 'true' ? 1 : 0;
      params.push(isActiveValue);
    }

    if (search) {
      conditions.push(`(
        d.name LIKE ? OR 
        d.code LIKE ? OR 
        d.description LIKE ?
      )`);
      const searchTerm = `%${search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM departments d
      ${whereClause}
    `;
    const countResult = await executeQuery(countQuery, params);
    const total = countResult[0].total;

    // Get departments with statistics
    const departmentsQuery = `
      SELECT 
        d.*,
        CONCAT(eh.first_name, ' ', eh.last_name) as department_head_name,
        COUNT(e.id) as total_employees,
        COUNT(CASE WHEN e.employment_status = 'active' THEN 1 END) as active_employees,
        COUNT(CASE WHEN e.employment_status = 'inactive' THEN 1 END) as inactive_employees,
        AVG(e.salary) as average_salary
      FROM departments d
      LEFT JOIN employees eh ON d.department_head_id = eh.id
      LEFT JOIN employees e ON d.id = e.department_id
      ${whereClause}
      GROUP BY d.id, d.name, d.code, d.description, d.department_head_id, d.budget, 
               d.location, d.phone, d.email, d.is_active, d.created_at, d.updated_at,
               eh.first_name, eh.last_name
      ORDER BY d.${sort_by} ${sort_order}
      LIMIT ? OFFSET ?
    `;

    console.log('WHERE clause:', whereClause);
    console.log('Params array:', params);
    console.log('Final query:', departmentsQuery);
    console.log('Final params:', [...params, Number(limit), offset]);
    console.log('Parameter count:', [...params, Number(limit), offset].length);
    console.log('Placeholder count:', (departmentsQuery.match(/\?/g) || []).length);

    const departments = await executeQuery(departmentsQuery, [...params, Number(limit), offset]);

    const response: DepartmentListResponse = {
      departments: departments as DepartmentWithStats[],
      total,
      page: Number(page),
      limit: Number(limit),
      total_pages: Math.ceil(total / Number(limit))
    };

    res.json(response);
  } catch (error) {
    console.error('Get departments error:', error);
    res.status(500).json({ error: 'Failed to get departments' });
  }
};

// Get department by ID
export const getDepartmentById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    const departments = await executeQuery(`
      SELECT 
        d.*,
        CONCAT(eh.first_name, ' ', eh.last_name) as department_head_name,
        eh.employee_id as department_head_employee_id,
        COUNT(e.id) as total_employees,
        COUNT(CASE WHEN e.employment_status = 'active' THEN 1 END) as active_employees,
        COUNT(CASE WHEN e.employment_status = 'inactive' THEN 1 END) as inactive_employees,
        AVG(e.salary) as average_salary
      FROM departments d
      LEFT JOIN employees eh ON d.department_head_id = eh.id
      LEFT JOIN employees e ON d.id = e.department_id
      WHERE d.id = ?
      GROUP BY d.id, d.name, d.code, d.description, d.department_head_id, d.budget, 
               d.location, d.phone, d.email, d.is_active, d.created_at, d.updated_at,
               eh.first_name, eh.last_name, eh.employee_id
    `, [id]);

    if (!departments || departments.length === 0) {
      res.status(404).json({ error: 'Department not found' });
      return;
    }

    res.json({ department: departments[0] });
  } catch (error) {
    console.error('Get department error:', error);
    res.status(500).json({ error: 'Failed to get department' });
  }
};

// Create new department
export const createDepartment = async (req: Request, res: Response): Promise<void> => {
  try {
    const departmentData: CreateDepartmentRequest = req.body;

    // Check if department code already exists
    const existingDepartments = await executeQuery(
      'SELECT id FROM departments WHERE code = ?',
      [departmentData.code]
    );

    if (existingDepartments && existingDepartments.length > 0) {
      res.status(409).json({ error: 'Department code already exists' });
      return;
    }

    // Check if department head exists
    if (departmentData.department_head_id) {
      const employees = await executeQuery(
        'SELECT id FROM employees WHERE id = ? AND employment_status = "active"',
        [departmentData.department_head_id]
      );

      if (!employees || employees.length === 0) {
        res.status(400).json({ error: 'Invalid department head ID' });
        return;
      }
    }

    // Insert new department
    const insertQuery = `
      INSERT INTO departments (
        name, code, description, department_head_id, budget, location, phone, email, is_active
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const insertParams = [
      departmentData.name,
      departmentData.code,
      departmentData.description || null,
      departmentData.department_head_id || null,
      departmentData.budget || 0,
      departmentData.location || null,
      departmentData.phone || null,
      departmentData.email || null,
      departmentData.is_active !== undefined ? departmentData.is_active : true
    ];

    const result = await executeQuery(insertQuery, insertParams);

    // Get the created department
    const newDepartments = await executeQuery(`
      SELECT 
        d.*,
        CONCAT(eh.first_name, ' ', eh.last_name) as department_head_name
      FROM departments d
      LEFT JOIN employees eh ON d.department_head_id = eh.id
      WHERE d.id = ?
    `, [result.insertId]);

    res.status(201).json({
      message: 'Department created successfully',
      department: newDepartments[0]
    });
  } catch (error) {
    console.error('Create department error:', error);
    res.status(500).json({ error: 'Failed to create department' });
  }
};

// Update department
export const updateDepartment = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const updateData: UpdateDepartmentRequest = req.body;

    // Check if department exists
    const existingDepartments = await executeQuery(
      'SELECT id, code FROM departments WHERE id = ?',
      [id]
    );

    if (!existingDepartments || existingDepartments.length === 0) {
      res.status(404).json({ error: 'Department not found' });
      return;
    }

    const existingDepartment = existingDepartments[0];

    // Check if code is being changed and if it already exists
    if (updateData.code && updateData.code !== existingDepartment.code) {
      const duplicateDepartments = await executeQuery(
        'SELECT id FROM departments WHERE code = ? AND id != ?',
        [updateData.code, id]
      );

      if (duplicateDepartments && duplicateDepartments.length > 0) {
        res.status(409).json({ error: 'Department code already exists' });
        return;
      }
    }

    // Check if department head exists
    if (updateData.department_head_id) {
      const employees = await executeQuery(
        'SELECT id FROM employees WHERE id = ? AND employment_status = "active"',
        [updateData.department_head_id]
      );

      if (!employees || employees.length === 0) {
        res.status(400).json({ error: 'Invalid department head ID' });
        return;
      }
    }

    // Build update query
    const updates: string[] = [];
    const values: any[] = [];

    Object.keys(updateData).forEach(key => {
      if (updateData[key as keyof UpdateDepartmentRequest] !== undefined) {
        updates.push(`${key} = ?`);
        values.push(updateData[key as keyof UpdateDepartmentRequest]);
      }
    });

    if (updates.length === 0) {
      res.status(400).json({ error: 'No valid fields to update' });
      return;
    }

    updates.push('updated_at = CURRENT_TIMESTAMP');
    values.push(id);

    await executeQuery(
      `UPDATE departments SET ${updates.join(', ')} WHERE id = ?`,
      values
    );

    // Get updated department
    const updatedDepartments = await executeQuery(`
      SELECT 
        d.*,
        CONCAT(eh.first_name, ' ', eh.last_name) as department_head_name
      FROM departments d
      LEFT JOIN employees eh ON d.department_head_id = eh.id
      WHERE d.id = ?
    `, [id]);

    res.json({
      message: 'Department updated successfully',
      department: updatedDepartments[0]
    });
  } catch (error) {
    console.error('Update department error:', error);
    res.status(500).json({ error: 'Failed to update department' });
  }
};

// Delete department (soft delete by setting is_active to false)
export const deleteDepartment = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    // Check if department exists
    const departments = await executeQuery(
      'SELECT id, is_active FROM departments WHERE id = ?',
      [id]
    );

    if (!departments || departments.length === 0) {
      res.status(404).json({ error: 'Department not found' });
      return;
    }

    // Check if department has active employees
    const activeEmployees = await executeQuery(
      'SELECT COUNT(*) as count FROM employees WHERE department_id = ? AND employment_status = "active"',
      [id]
    );

    if (activeEmployees[0].count > 0) {
      res.status(400).json({ 
        error: 'Cannot delete department with active employees',
        active_employees: activeEmployees[0].count
      });
      return;
    }

    // Soft delete by setting is_active to false
    await executeQuery(
      'UPDATE departments SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [id]
    );

    res.json({ message: 'Department deleted successfully' });
  } catch (error) {
    console.error('Delete department error:', error);
    res.status(500).json({ error: 'Failed to delete department' });
  }
};

// Get department employees
export const getDepartmentEmployees = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { employment_status = 'active', page = 1, limit = 10 } = req.query;

    // Check if department exists
    const departments = await executeQuery(
      'SELECT id, name FROM departments WHERE id = ?',
      [id]
    );

    if (!departments || departments.length === 0) {
      res.status(404).json({ error: 'Department not found' });
      return;
    }

    const offset = (Number(page) - 1) * Number(limit);

    // Get total count
    const countResult = await executeQuery(
      'SELECT COUNT(*) as total FROM employees WHERE department_id = ? AND employment_status = ?',
      [id, employment_status]
    );
    const total = countResult[0].total;

    // Get employees
    const employees = await executeQuery(`
      SELECT 
        e.id, e.employee_id, e.first_name, e.last_name, e.position, 
        e.employment_type, e.employment_status, e.hire_date, e.salary, e.email, e.phone
      FROM employees e
      WHERE e.department_id = ? AND e.employment_status = ?
      ORDER BY e.first_name, e.last_name
      LIMIT ? OFFSET ?
    `, [id, employment_status, Number(limit), offset]);

    res.json({
      department: departments[0],
      employees,
      total,
      page: Number(page),
      limit: Number(limit),
      total_pages: Math.ceil(total / Number(limit))
    });
  } catch (error) {
    console.error('Get department employees error:', error);
    res.status(500).json({ error: 'Failed to get department employees' });
  }
};

// Get department statistics
export const getDepartmentStats = async (req: Request, res: Response): Promise<void> => {
  try {
    const stats = await executeQuery(`
      SELECT 
        COUNT(*) as total_departments,
        COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_departments,
        COUNT(CASE WHEN is_active = FALSE THEN 1 END) as inactive_departments,
        AVG(budget) as average_budget,
        SUM(budget) as total_budget,
        COUNT(CASE WHEN department_head_id IS NOT NULL THEN 1 END) as departments_with_heads
      FROM departments
    `);

    const employeeStats = await executeQuery(`
      SELECT 
        d.name as department_name,
        COUNT(e.id) as employee_count,
        AVG(e.salary) as average_salary
      FROM departments d
      LEFT JOIN employees e ON d.id = e.department_id AND e.employment_status = 'active'
      WHERE d.is_active = TRUE
      GROUP BY d.id, d.name
      ORDER BY employee_count DESC
    `);

    res.json({ 
      overall_stats: stats[0],
      department_breakdown: employeeStats
    });
  } catch (error) {
    console.error('Get department stats error:', error);
    res.status(500).json({ error: 'Failed to get department statistics' });
  }
};
