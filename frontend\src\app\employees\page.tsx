'use client';

import React, { useState, useEffect } from 'react';
import { withAuth } from '@/hooks/useAuth';
import DashboardLayout from '@/components/layout/DashboardLayout';
import DataTable from '@/components/tables/DataTable';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { ConfirmationModal } from '@/components/ui/Modal';
import { EmployeeWithDepartment, TableColumn, Department } from '@/types';
import { apiService } from '@/services/api';
import { formatCurrency, formatDate, getEmploymentStatusColor, getEmploymentTypeColor } from '@/lib/utils';
import { useToastHelpers } from '@/components/ui/Toast';

function EmployeesPage() {
  const { success, error } = useToastHelpers();
  const [employees, setEmployees] = useState<EmployeeWithDepartment[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedEmployees, setSelectedEmployees] = useState<number[]>([]);
  const [showBulkDeleteModal, setShowBulkDeleteModal] = useState(false);
  const [bulkActionLoading, setBulkActionLoading] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
  });
  const [filters, setFilters] = useState({
    search: '',
    department_id: '',
    employment_status: '',
    employment_type: '',
  });

  const columns: TableColumn<EmployeeWithDepartment>[] = [
    {
      key: 'select',
      label: (
        <input
          type="checkbox"
          checked={selectedEmployees.length === employees.length && employees.length > 0}
          onChange={handleSelectAll}
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
      ),
      render: (_, row) => (
        <input
          type="checkbox"
          checked={selectedEmployees.includes(row.id)}
          onChange={() => handleSelectEmployee(row.id)}
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
      ),
    },
    {
      key: 'employee_id',
      label: 'Employee ID',
      sortable: true,
      render: (value) => (
        <span className="font-mono text-sm font-medium">{value}</span>
      ),
    },
    {
      key: 'first_name',
      label: 'Name',
      sortable: true,
      render: (value, row) => (
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <span className="text-sm font-medium text-blue-800">
              {row.first_name.charAt(0)}{row.last_name.charAt(0)}
            </span>
          </div>
          <div>
            <div className="font-medium text-gray-900">
              {row.first_name} {row.last_name}
            </div>
            <div className="text-sm text-gray-500">{row.email}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'position',
      label: 'Position',
      sortable: true,
    },
    {
      key: 'department_name',
      label: 'Department',
      sortable: true,
      render: (value) => (
        <Badge variant="info" size="sm">{value}</Badge>
      ),
    },
    {
      key: 'employment_type',
      label: 'Type',
      sortable: true,
      render: (value) => (
        <Badge 
          variant="default" 
          size="sm"
          className={getEmploymentTypeColor(value)}
        >
          {value.replace('_', ' ').toUpperCase()}
        </Badge>
      ),
    },
    {
      key: 'employment_status',
      label: 'Status',
      sortable: true,
      render: (value) => (
        <Badge 
          variant="default" 
          size="sm"
          className={getEmploymentStatusColor(value)}
        >
          {value.toUpperCase()}
        </Badge>
      ),
    },
    {
      key: 'hire_date',
      label: 'Hire Date',
      sortable: true,
      render: (value) => formatDate(value),
    },
    {
      key: 'salary',
      label: 'Salary',
      sortable: true,
      render: (value) => value ? formatCurrency(value) : '-',
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (_, row) => (
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleViewEmployee(row.id)}
          >
            View
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleEditEmployee(row.id)}
          >
            Edit
          </Button>
        </div>
      ),
    },
  ];

  const fetchEmployees = async () => {
    try {
      setLoading(true);
      const response = await apiService.getEmployees({
        ...filters,
        page: pagination.page,
        limit: pagination.limit,
      });

      setEmployees(response.employees || []);
      setPagination(prev => ({
        ...prev,
        total: response.total || 0,
      }));
    } catch (err) {
      console.error('Failed to fetch employees:', err);
      error('Error', 'Failed to load employees');
    } finally {
      setLoading(false);
    }
  };

  const fetchDepartments = async () => {
    try {
      const response = await apiService.getDepartments();
      setDepartments(response.departments || []);
    } catch (err) {
      console.error('Failed to fetch departments:', err);
    }
  };

  const handleSelectEmployee = (employeeId: number) => {
    setSelectedEmployees(prev =>
      prev.includes(employeeId)
        ? prev.filter(id => id !== employeeId)
        : [...prev, employeeId]
    );
  };

  const handleSelectAll = () => {
    if (selectedEmployees.length === employees.length) {
      setSelectedEmployees([]);
    } else {
      setSelectedEmployees(employees.map(emp => emp.id));
    }
  };

  const handleBulkDelete = async () => {
    try {
      setBulkActionLoading(true);
      await Promise.all(
        selectedEmployees.map(id => apiService.deleteEmployee(id))
      );
      success('Success', `${selectedEmployees.length} employees deleted successfully`);
      setSelectedEmployees([]);
      setShowBulkDeleteModal(false);
      fetchEmployees();
    } catch (err) {
      console.error('Failed to delete employees:', err);
      error('Error', 'Failed to delete employees');
    } finally {
      setBulkActionLoading(false);
    }
  };

  const handleBulkStatusUpdate = async (status: string) => {
    try {
      setBulkActionLoading(true);
      await Promise.all(
        selectedEmployees.map(id =>
          apiService.updateEmployee(id, { employment_status: status })
        )
      );
      success('Success', `${selectedEmployees.length} employees updated successfully`);
      setSelectedEmployees([]);
      fetchEmployees();
    } catch (err) {
      console.error('Failed to update employees:', err);
      error('Error', 'Failed to update employees');
    } finally {
      setBulkActionLoading(false);
    }
  };

  const handleExportCSV = () => {
    try {
      const csvData = employees.map(emp => ({
        'Employee ID': emp.employee_id,
        'First Name': emp.first_name,
        'Last Name': emp.last_name,
        'Email': emp.email || '',
        'Phone': emp.phone || '',
        'Department': emp.department_name,
        'Position': emp.position,
        'Employment Type': emp.employment_type.replace('_', ' ').toUpperCase(),
        'Employment Status': emp.employment_status.toUpperCase(),
        'Hire Date': emp.hire_date,
        'Salary': emp.salary || '',
      }));

      const csvContent = [
        Object.keys(csvData[0]).join(','),
        ...csvData.map(row => Object.values(row).map(val => `"${val}"`).join(','))
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `employees_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      success('Success', 'Employee data exported successfully');
    } catch (err) {
      console.error('Failed to export data:', err);
      error('Error', 'Failed to export employee data');
    }
  };

  useEffect(() => {
    fetchEmployees();
    fetchDepartments();
  }, [pagination.page, pagination.limit, filters]);

  const handleSearch = (query: string) => {
    setFilters(prev => ({ ...prev, search: query }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleSort = (column: string, direction: 'ASC' | 'DESC') => {
    setFilters(prev => ({ 
      ...prev, 
      sort_by: column, 
      sort_order: direction 
    }));
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  const handleLimitChange = (limit: number) => {
    setPagination(prev => ({ ...prev, limit, page: 1 }));
  };

  const handleViewEmployee = (id: number) => {
    // Navigate to employee detail page
    window.location.href = `/employees/${id}`;
  };

  const handleEditEmployee = (id: number) => {
    // Navigate to employee edit page
    window.location.href = `/employees/${id}/edit`;
  };

  const handleAddEmployee = () => {
    // Navigate to add employee page
    window.location.href = '/employees/new';
  };

  return (
    <DashboardLayout title="Employees">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Employee Management</h1>
            <p className="text-gray-600">Manage hospital staff and employee information</p>
          </div>
          <div className="flex space-x-2">
            {selectedEmployees.length > 0 && (
              <>
                <div className="relative">
                  <select
                    onChange={(e) => {
                      if (e.target.value) {
                        handleBulkStatusUpdate(e.target.value);
                        e.target.value = '';
                      }
                    }}
                    disabled={bulkActionLoading}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Bulk Status Update</option>
                    <option value="active">Set Active</option>
                    <option value="inactive">Set Inactive</option>
                    <option value="terminated">Set Terminated</option>
                  </select>
                </div>
                <Button
                  variant="danger"
                  onClick={() => setShowBulkDeleteModal(true)}
                  disabled={bulkActionLoading}
                >
                  Delete Selected ({selectedEmployees.length})
                </Button>
              </>
            )}
            <Button variant="outline" onClick={handleExportCSV}>
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Export CSV
            </Button>
            <Button onClick={handleAddEmployee}>
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Add Employee
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{pagination.total}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {employees.filter(e => e.employment_status === 'active').length}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Full Time</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {employees.filter(e => e.employment_type === 'full_time').length}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">New This Month</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">
                {employees.filter(e => {
                  const hireDate = new Date(e.hire_date);
                  const now = new Date();
                  return hireDate.getMonth() === now.getMonth() && 
                         hireDate.getFullYear() === now.getFullYear();
                }).length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Department
                </label>
                <select
                  value={filters.department_id}
                  onChange={(e) => setFilters(prev => ({ ...prev, department_id: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Departments</option>
                  {departments.map(dept => (
                    <option key={dept.id} value={dept.id}>
                      {dept.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Employment Status
                </label>
                <select
                  value={filters.employment_status}
                  onChange={(e) => setFilters(prev => ({ ...prev, employment_status: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Statuses</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="terminated">Terminated</option>
                  <option value="resigned">Resigned</option>
                  <option value="retired">Retired</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Employment Type
                </label>
                <select
                  value={filters.employment_type}
                  onChange={(e) => setFilters(prev => ({ ...prev, employment_type: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Types</option>
                  <option value="full_time">Full Time</option>
                  <option value="part_time">Part Time</option>
                  <option value="contract">Contract</option>
                  <option value="intern">Intern</option>
                </select>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Search
                </label>
                <input
                  type="text"
                  placeholder="Search by name, email, or employee ID..."
                  value={filters.search}
                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Employee Table */}
        <DataTable
          data={employees}
          columns={columns}
          loading={loading}
          onSort={handleSort}
          pagination={{
            page: pagination.page,
            limit: pagination.limit,
            total: pagination.total,
            onPageChange: handlePageChange,
            onLimitChange: handleLimitChange,
          }}
        />

        {/* Bulk Delete Confirmation Modal */}
        <ConfirmationModal
          isOpen={showBulkDeleteModal}
          onClose={() => setShowBulkDeleteModal(false)}
          onConfirm={handleBulkDelete}
          title="Delete Selected Employees"
          message={`Are you sure you want to delete ${selectedEmployees.length} selected employees? This action cannot be undone.`}
          confirmText="Delete"
          type="danger"
          loading={bulkActionLoading}
        />
      </div>
    </DashboardLayout>
  );
}

export default withAuth(EmployeesPage, ['admin', 'hr', 'department_head']);
