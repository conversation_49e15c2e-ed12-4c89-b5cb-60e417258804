'use client';

import React, { useState, useEffect } from 'react';
import { withAuth } from '@/hooks/useAuth';
import DashboardLayout from '@/components/layout/DashboardLayout';
import DataTable from '@/components/tables/DataTable';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { EmployeeWithDepartment, TableColumn } from '@/types';
import { apiService } from '@/services/api';
import { formatCurrency, formatDate, getEmploymentStatusColor, getEmploymentTypeColor } from '@/lib/utils';

function EmployeesPage() {
  const [employees, setEmployees] = useState<EmployeeWithDepartment[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
  });
  const [filters, setFilters] = useState({
    search: '',
    department_id: '',
    employment_status: '',
    employment_type: '',
  });

  const columns: TableColumn<EmployeeWithDepartment>[] = [
    {
      key: 'employee_id',
      label: 'Employee ID',
      sortable: true,
      render: (value) => (
        <span className="font-mono text-sm font-medium">{value}</span>
      ),
    },
    {
      key: 'first_name',
      label: 'Name',
      sortable: true,
      render: (value, row) => (
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <span className="text-sm font-medium text-blue-800">
              {row.first_name.charAt(0)}{row.last_name.charAt(0)}
            </span>
          </div>
          <div>
            <div className="font-medium text-gray-900">
              {row.first_name} {row.last_name}
            </div>
            <div className="text-sm text-gray-500">{row.email}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'position',
      label: 'Position',
      sortable: true,
    },
    {
      key: 'department_name',
      label: 'Department',
      sortable: true,
      render: (value) => (
        <Badge variant="info" size="sm">{value}</Badge>
      ),
    },
    {
      key: 'employment_type',
      label: 'Type',
      sortable: true,
      render: (value) => (
        <Badge 
          variant="default" 
          size="sm"
          className={getEmploymentTypeColor(value)}
        >
          {value.replace('_', ' ').toUpperCase()}
        </Badge>
      ),
    },
    {
      key: 'employment_status',
      label: 'Status',
      sortable: true,
      render: (value) => (
        <Badge 
          variant="default" 
          size="sm"
          className={getEmploymentStatusColor(value)}
        >
          {value.toUpperCase()}
        </Badge>
      ),
    },
    {
      key: 'hire_date',
      label: 'Hire Date',
      sortable: true,
      render: (value) => formatDate(value),
    },
    {
      key: 'salary',
      label: 'Salary',
      sortable: true,
      render: (value) => value ? formatCurrency(value) : '-',
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (_, row) => (
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleViewEmployee(row.id)}
          >
            View
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleEditEmployee(row.id)}
          >
            Edit
          </Button>
        </div>
      ),
    },
  ];

  const fetchEmployees = async () => {
    try {
      setLoading(true);
      const response = await apiService.getEmployees({
        ...filters,
        page: pagination.page,
        limit: pagination.limit,
      });
      
      setEmployees(response.employees || []);
      setPagination(prev => ({
        ...prev,
        total: response.total || 0,
      }));
    } catch (error) {
      console.error('Failed to fetch employees:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchEmployees();
  }, [pagination.page, pagination.limit, filters]);

  const handleSearch = (query: string) => {
    setFilters(prev => ({ ...prev, search: query }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleSort = (column: string, direction: 'ASC' | 'DESC') => {
    setFilters(prev => ({ 
      ...prev, 
      sort_by: column, 
      sort_order: direction 
    }));
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  const handleLimitChange = (limit: number) => {
    setPagination(prev => ({ ...prev, limit, page: 1 }));
  };

  const handleViewEmployee = (id: number) => {
    // Navigate to employee detail page
    window.location.href = `/employees/${id}`;
  };

  const handleEditEmployee = (id: number) => {
    // Navigate to employee edit page
    window.location.href = `/employees/${id}/edit`;
  };

  const handleAddEmployee = () => {
    // Navigate to add employee page
    window.location.href = '/employees/new';
  };

  return (
    <DashboardLayout title="Employees">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Employee Management</h1>
            <p className="text-gray-600">Manage hospital staff and employee information</p>
          </div>
          <Button onClick={handleAddEmployee}>
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add Employee
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{pagination.total}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {employees.filter(e => e.employment_status === 'active').length}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Full Time</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {employees.filter(e => e.employment_type === 'full_time').length}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">New This Month</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">
                {employees.filter(e => {
                  const hireDate = new Date(e.hire_date);
                  const now = new Date();
                  return hireDate.getMonth() === now.getMonth() && 
                         hireDate.getFullYear() === now.getFullYear();
                }).length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Employment Status
                </label>
                <select
                  value={filters.employment_status}
                  onChange={(e) => setFilters(prev => ({ ...prev, employment_status: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Statuses</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="terminated">Terminated</option>
                  <option value="resigned">Resigned</option>
                  <option value="retired">Retired</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Employment Type
                </label>
                <select
                  value={filters.employment_type}
                  onChange={(e) => setFilters(prev => ({ ...prev, employment_type: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Types</option>
                  <option value="full_time">Full Time</option>
                  <option value="part_time">Part Time</option>
                  <option value="contract">Contract</option>
                  <option value="intern">Intern</option>
                </select>
              </div>
              
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Search
                </label>
                <input
                  type="text"
                  placeholder="Search by name, email, or employee ID..."
                  value={filters.search}
                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Employee Table */}
        <DataTable
          data={employees}
          columns={columns}
          loading={loading}
          onSort={handleSort}
          pagination={{
            page: pagination.page,
            limit: pagination.limit,
            total: pagination.total,
            onPageChange: handlePageChange,
            onLimitChange: handleLimitChange,
          }}
        />
      </div>
    </DashboardLayout>
  );
}

export default withAuth(EmployeesPage, ['admin', 'hr', 'department_head']);
