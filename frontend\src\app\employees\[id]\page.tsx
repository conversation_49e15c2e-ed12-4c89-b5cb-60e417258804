'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { withAuth } from '@/hooks/useAuth';
import DashboardLayout from '@/components/layout/DashboardLayout';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { EmployeeWithDepartment } from '@/types';
import { apiService } from '@/services/api';
import { formatCurrency, formatDate, calculateAge, getEmploymentStatusColor, getEmploymentTypeColor } from '@/lib/utils';

function EmployeeDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [employee, setEmployee] = useState<EmployeeWithDepartment | null>(null);
  const [loading, setLoading] = useState(true);

  const employeeId = params.id as string;

  useEffect(() => {
    if (employeeId) {
      fetchEmployee();
    }
  }, [employeeId]);

  const fetchEmployee = async () => {
    try {
      setLoading(true);
      const response = await apiService.getEmployee(Number(employeeId));
      setEmployee(response.employee);
    } catch (error) {
      console.error('Failed to fetch employee:', error);
      alert('Failed to load employee details');
      router.push('/employees');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    router.push(`/employees/${employeeId}/edit`);
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this employee? This action cannot be undone.')) {
      return;
    }

    try {
      await apiService.deleteEmployee(Number(employeeId));
      alert('Employee deleted successfully');
      router.push('/employees');
    } catch (error) {
      console.error('Failed to delete employee:', error);
      alert('Failed to delete employee');
    }
  };

  if (loading) {
    return (
      <DashboardLayout title="Employee Details">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (!employee) {
    return (
      <DashboardLayout title="Employee Not Found">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Employee Not Found</h2>
          <p className="text-gray-600 mb-6">The employee you're looking for doesn't exist.</p>
          <Button onClick={() => router.push('/employees')}>
            Back to Employees
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title={`${employee.first_name} ${employee.last_name}`}>
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex justify-between items-start">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-xl font-bold text-blue-800">
                {employee.first_name.charAt(0)}{employee.last_name.charAt(0)}
              </span>
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {employee.first_name} {employee.middle_name} {employee.last_name}
              </h1>
              <p className="text-gray-600">{employee.position}</p>
              <div className="flex items-center space-x-2 mt-1">
                <Badge variant="info" size="sm">{employee.department_name}</Badge>
                <Badge 
                  variant="default" 
                  size="sm"
                  className={getEmploymentStatusColor(employee.employment_status)}
                >
                  {employee.employment_status.toUpperCase()}
                </Badge>
              </div>
            </div>
          </div>
          
          <div className="flex space-x-2">
            <Button variant="outline" onClick={handleEdit}>
              Edit
            </Button>
            <Button variant="danger" onClick={handleDelete}>
              Delete
            </Button>
          </div>
        </div>

        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-500">Employee ID</label>
                <p className="mt-1 text-sm text-gray-900 font-mono">{employee.employee_id}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-500">Date of Birth</label>
                <p className="mt-1 text-sm text-gray-900">
                  {employee.date_of_birth ? (
                    <>
                      {formatDate(employee.date_of_birth)} 
                      <span className="text-gray-500 ml-2">
                        (Age: {calculateAge(employee.date_of_birth)})
                      </span>
                    </>
                  ) : 'Not specified'}
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-500">Gender</label>
                <p className="mt-1 text-sm text-gray-900 capitalize">
                  {employee.gender || 'Not specified'}
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-500">Marital Status</label>
                <p className="mt-1 text-sm text-gray-900 capitalize">
                  {employee.marital_status || 'Not specified'}
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-500">Nationality</label>
                <p className="mt-1 text-sm text-gray-900">
                  {employee.nationality || 'Not specified'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle>Contact Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-500">Email</label>
                <p className="mt-1 text-sm text-gray-900">
                  {employee.email ? (
                    <a href={`mailto:${employee.email}`} className="text-blue-600 hover:underline">
                      {employee.email}
                    </a>
                  ) : 'Not specified'}
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-500">Phone</label>
                <p className="mt-1 text-sm text-gray-900">
                  {employee.phone ? (
                    <a href={`tel:${employee.phone}`} className="text-blue-600 hover:underline">
                      {employee.phone}
                    </a>
                  ) : 'Not specified'}
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-500">Emergency Contact</label>
                <p className="mt-1 text-sm text-gray-900">
                  {employee.emergency_contact_name || 'Not specified'}
                  {employee.emergency_contact_relationship && (
                    <span className="text-gray-500 ml-1">
                      ({employee.emergency_contact_relationship})
                    </span>
                  )}
                </p>
                {employee.emergency_contact_phone && (
                  <p className="text-sm text-blue-600">
                    <a href={`tel:${employee.emergency_contact_phone}`} className="hover:underline">
                      {employee.emergency_contact_phone}
                    </a>
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Address */}
        {(employee.address_line1 || employee.city || employee.state) && (
          <Card>
            <CardHeader>
              <CardTitle>Address</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm text-gray-900">
                {employee.address_line1 && <p>{employee.address_line1}</p>}
                {employee.address_line2 && <p>{employee.address_line2}</p>}
                <p>
                  {[employee.city, employee.state, employee.postal_code]
                    .filter(Boolean)
                    .join(', ')}
                </p>
                {employee.country && <p>{employee.country}</p>}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Employment Details */}
        <Card>
          <CardHeader>
            <CardTitle>Employment Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-500">Department</label>
                <p className="mt-1 text-sm text-gray-900">{employee.department_name}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-500">Position</label>
                <p className="mt-1 text-sm text-gray-900">{employee.position}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-500">Employment Type</label>
                <Badge 
                  variant="default" 
                  size="sm"
                  className={getEmploymentTypeColor(employee.employment_type)}
                >
                  {employee.employment_type.replace('_', ' ').toUpperCase()}
                </Badge>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-500">Employment Status</label>
                <Badge 
                  variant="default" 
                  size="sm"
                  className={getEmploymentStatusColor(employee.employment_status)}
                >
                  {employee.employment_status.toUpperCase()}
                </Badge>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-500">Hire Date</label>
                <p className="mt-1 text-sm text-gray-900">{formatDate(employee.hire_date)}</p>
              </div>
              
              {employee.termination_date && (
                <div>
                  <label className="block text-sm font-medium text-gray-500">Termination Date</label>
                  <p className="mt-1 text-sm text-gray-900">{formatDate(employee.termination_date)}</p>
                </div>
              )}
              
              {employee.salary && (
                <div>
                  <label className="block text-sm font-medium text-gray-500">Salary</label>
                  <p className="mt-1 text-sm text-gray-900">{formatCurrency(employee.salary)}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}

export default withAuth(EmployeeDetailPage, ['admin', 'hr', 'department_head']);
