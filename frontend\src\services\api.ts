import axios, { AxiosInstance, AxiosResponse } from 'axios';
import Cookies from 'js-cookie';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = Cookies.get('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid
          Cookies.remove('auth_token');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Generic API methods
  async get<T>(url: string, params?: any): Promise<T> {
    const response: AxiosResponse<T> = await this.api.get(url, { params });
    return response.data;
  }

  async post<T>(url: string, data?: any): Promise<T> {
    const response: AxiosResponse<T> = await this.api.post(url, data);
    return response.data;
  }

  async put<T>(url: string, data?: any): Promise<T> {
    const response: AxiosResponse<T> = await this.api.put(url, data);
    return response.data;
  }

  async delete<T>(url: string): Promise<T> {
    const response: AxiosResponse<T> = await this.api.delete(url);
    return response.data;
  }

  // Authentication methods
  async login(email: string, password: string) {
    return this.post('/auth/login', { email, password });
  }

  async register(userData: any) {
    return this.post('/auth/register', userData);
  }

  async getProfile() {
    return this.get('/auth/profile');
  }

  async updateProfile(data: any) {
    return this.put('/auth/profile', data);
  }

  async logout() {
    return this.post('/auth/logout');
  }

  async refreshToken() {
    return this.post('/auth/refresh');
  }

  // Employee methods
  async getEmployees(filters?: any) {
    return this.get('/employees', filters);
  }

  async getEmployee(id: number) {
    return this.get(`/employees/${id}`);
  }

  async createEmployee(data: any) {
    return this.post('/employees', data);
  }

  async updateEmployee(id: number, data: any) {
    return this.put(`/employees/${id}`, data);
  }

  async deleteEmployee(id: number) {
    return this.delete(`/employees/${id}`);
  }

  async getEmployeeStats() {
    return this.get('/employees/stats');
  }

  // Department methods
  async getDepartments(filters?: any) {
    return this.get('/departments', filters);
  }

  async getDepartment(id: number) {
    return this.get(`/departments/${id}`);
  }

  async createDepartment(data: any) {
    return this.post('/departments', data);
  }

  async updateDepartment(id: number, data: any) {
    return this.put(`/departments/${id}`, data);
  }

  async deleteDepartment(id: number) {
    return this.delete(`/departments/${id}`);
  }

  async getDepartmentStats() {
    return this.get('/departments/stats');
  }

  async getDepartmentEmployees(id: number, filters?: any) {
    return this.get(`/departments/${id}/employees`, filters);
  }

  // File upload methods
  async uploadFile(file: File, endpoint: string) {
    const formData = new FormData();
    formData.append('file', file);

    const response = await this.api.post(endpoint, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  }

  // Employee file upload methods
  async uploadEmployeePhoto(employeeId: number, file: File) {
    return this.uploadFile(file, `/employees/${employeeId}/photo`);
  }

  async uploadEmployeeDocument(employeeId: number, file: File, documentType: 'resume' | 'contract') {
    return this.uploadFile(file, `/employees/${employeeId}/documents/${documentType}`);
  }

  async deleteEmployeePhoto(employeeId: number) {
    return this.delete(`/employees/${employeeId}/photo`);
  }

  async deleteEmployeeDocument(employeeId: number, documentType: 'resume' | 'contract') {
    return this.delete(`/employees/${employeeId}/documents/${documentType}`);
  }

  // Health check
  async healthCheck() {
    return this.get('/health');
  }
}

export const apiService = new ApiService();
export default apiService;
