'use client';

import React, { useState, useEffect } from 'react';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Select from '@/components/ui/Select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { CreateEmployeeRequest, Department } from '@/types';
import { apiService } from '@/services/api';

interface EmployeeFormProps {
  initialData?: Partial<CreateEmployeeRequest>;
  onSubmit: (data: CreateEmployeeRequest) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  mode: 'create' | 'edit';
}

export default function EmployeeForm({
  initialData,
  onSubmit,
  onCancel,
  loading = false,
  mode
}: EmployeeFormProps) {
  const [formData, setFormData] = useState<CreateEmployeeRequest>({
    employee_id: '',
    first_name: '',
    last_name: '',
    middle_name: '',
    date_of_birth: '',
    gender: 'male',
    marital_status: 'single',
    nationality: 'Indonesian',
    email: '',
    phone: '',
    emergency_contact_name: '',
    emergency_contact_phone: '',
    emergency_contact_relationship: '',
    address_line1: '',
    address_line2: '',
    city: '',
    state: '',
    postal_code: '',
    country: 'Indonesia',
    department_id: 0,
    position: '',
    employment_type: 'full_time',
    employment_status: 'active',
    hire_date: '',
    salary: 0,
    ...initialData,
  });

  const [departments, setDepartments] = useState<Department[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    fetchDepartments();
  }, []);

  const fetchDepartments = async () => {
    try {
      const response = await apiService.getDepartments({ is_active: true });
      setDepartments(response.departments || []);
    } catch (error) {
      console.error('Failed to fetch departments:', error);
    }
  };

  const handleInputChange = (field: keyof CreateEmployeeRequest, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Required fields
    if (!formData.employee_id) newErrors.employee_id = 'Employee ID is required';
    if (!formData.first_name) newErrors.first_name = 'First name is required';
    if (!formData.last_name) newErrors.last_name = 'Last name is required';
    if (!formData.department_id) newErrors.department_id = 'Department is required';
    if (!formData.position) newErrors.position = 'Position is required';
    if (!formData.hire_date) newErrors.hire_date = 'Hire date is required';

    // Email validation
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Invalid email format';
    }

    // Phone validation
    if (formData.phone && !/^(\+62|62|0)[0-9]{8,13}$/.test(formData.phone.replace(/[\s\-\(\)]/g, ''))) {
      newErrors.phone = 'Invalid phone number format';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error: any) {
      console.error('Form submission error:', error);
      // Handle validation errors from server
      if (error.response?.data?.details) {
        const serverErrors: Record<string, string> = {};
        error.response.data.details.forEach((detail: any) => {
          serverErrors[detail.path] = detail.msg;
        });
        setErrors(serverErrors);
      }
    }
  };

  const genderOptions = [
    { value: 'male', label: 'Male' },
    { value: 'female', label: 'Female' },
    { value: 'other', label: 'Other' },
  ];

  const maritalStatusOptions = [
    { value: 'single', label: 'Single' },
    { value: 'married', label: 'Married' },
    { value: 'divorced', label: 'Divorced' },
    { value: 'widowed', label: 'Widowed' },
  ];

  const employmentTypeOptions = [
    { value: 'full_time', label: 'Full Time' },
    { value: 'part_time', label: 'Part Time' },
    { value: 'contract', label: 'Contract' },
    { value: 'intern', label: 'Intern' },
  ];

  const employmentStatusOptions = [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
    { value: 'terminated', label: 'Terminated' },
    { value: 'resigned', label: 'Resigned' },
    { value: 'retired', label: 'Retired' },
  ];

  const departmentOptions = departments.map(dept => ({
    value: dept.id,
    label: dept.name,
  }));

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="Employee ID"
            value={formData.employee_id}
            onChange={(e) => handleInputChange('employee_id', e.target.value)}
            error={errors.employee_id}
            placeholder="EMP001"
            required
          />
          
          <div></div>
          
          <Input
            label="First Name"
            value={formData.first_name}
            onChange={(e) => handleInputChange('first_name', e.target.value)}
            error={errors.first_name}
            required
          />
          
          <Input
            label="Last Name"
            value={formData.last_name}
            onChange={(e) => handleInputChange('last_name', e.target.value)}
            error={errors.last_name}
            required
          />
          
          <Input
            label="Middle Name"
            value={formData.middle_name}
            onChange={(e) => handleInputChange('middle_name', e.target.value)}
          />
          
          <Input
            label="Date of Birth"
            type="date"
            value={formData.date_of_birth}
            onChange={(e) => handleInputChange('date_of_birth', e.target.value)}
          />
          
          <Select
            label="Gender"
            value={formData.gender}
            onChange={(e) => handleInputChange('gender', e.target.value)}
            options={genderOptions}
          />
          
          <Select
            label="Marital Status"
            value={formData.marital_status}
            onChange={(e) => handleInputChange('marital_status', e.target.value)}
            options={maritalStatusOptions}
          />
          
          <Input
            label="Nationality"
            value={formData.nationality}
            onChange={(e) => handleInputChange('nationality', e.target.value)}
          />
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card>
        <CardHeader>
          <CardTitle>Contact Information</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="Email"
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            error={errors.email}
          />
          
          <Input
            label="Phone"
            value={formData.phone}
            onChange={(e) => handleInputChange('phone', e.target.value)}
            error={errors.phone}
            placeholder="+62-812-3456-7890"
          />
          
          <Input
            label="Emergency Contact Name"
            value={formData.emergency_contact_name}
            onChange={(e) => handleInputChange('emergency_contact_name', e.target.value)}
          />
          
          <Input
            label="Emergency Contact Phone"
            value={formData.emergency_contact_phone}
            onChange={(e) => handleInputChange('emergency_contact_phone', e.target.value)}
          />
          
          <Input
            label="Emergency Contact Relationship"
            value={formData.emergency_contact_relationship}
            onChange={(e) => handleInputChange('emergency_contact_relationship', e.target.value)}
            placeholder="Spouse, Parent, Sibling, etc."
          />
        </CardContent>
      </Card>

      {/* Address */}
      <Card>
        <CardHeader>
          <CardTitle>Address</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="md:col-span-2">
            <Input
              label="Address Line 1"
              value={formData.address_line1}
              onChange={(e) => handleInputChange('address_line1', e.target.value)}
            />
          </div>
          
          <div className="md:col-span-2">
            <Input
              label="Address Line 2"
              value={formData.address_line2}
              onChange={(e) => handleInputChange('address_line2', e.target.value)}
            />
          </div>
          
          <Input
            label="City"
            value={formData.city}
            onChange={(e) => handleInputChange('city', e.target.value)}
          />
          
          <Input
            label="State/Province"
            value={formData.state}
            onChange={(e) => handleInputChange('state', e.target.value)}
          />
          
          <Input
            label="Postal Code"
            value={formData.postal_code}
            onChange={(e) => handleInputChange('postal_code', e.target.value)}
          />
          
          <Input
            label="Country"
            value={formData.country}
            onChange={(e) => handleInputChange('country', e.target.value)}
          />
        </CardContent>
      </Card>

      {/* Employment Details */}
      <Card>
        <CardHeader>
          <CardTitle>Employment Details</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Select
            label="Department"
            value={formData.department_id}
            onChange={(e) => handleInputChange('department_id', Number(e.target.value))}
            options={departmentOptions}
            error={errors.department_id}
            placeholder="Select Department"
            required
          />
          
          <Input
            label="Position"
            value={formData.position}
            onChange={(e) => handleInputChange('position', e.target.value)}
            error={errors.position}
            required
          />
          
          <Select
            label="Employment Type"
            value={formData.employment_type}
            onChange={(e) => handleInputChange('employment_type', e.target.value)}
            options={employmentTypeOptions}
          />
          
          <Select
            label="Employment Status"
            value={formData.employment_status}
            onChange={(e) => handleInputChange('employment_status', e.target.value)}
            options={employmentStatusOptions}
          />
          
          <Input
            label="Hire Date"
            type="date"
            value={formData.hire_date}
            onChange={(e) => handleInputChange('hire_date', e.target.value)}
            error={errors.hire_date}
            required
          />
          
          <Input
            label="Salary (IDR)"
            type="number"
            value={formData.salary}
            onChange={(e) => handleInputChange('salary', Number(e.target.value))}
            placeholder="15000000"
          />
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={loading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          loading={loading}
        >
          {mode === 'create' ? 'Create Employee' : 'Update Employee'}
        </Button>
      </div>
    </form>
  );
}
