"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.refreshToken = exports.logout = exports.updateProfile = exports.getProfile = exports.login = exports.register = void 0;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const database_1 = require("../config/database");
// Generate JWT token
const generateToken = (user) => {
    const payload = {
        userId: user.id,
        email: user.email,
        role: user.role
    };
    const secret = process.env.JWT_SECRET;
    return jsonwebtoken_1.default.sign(payload, secret, { expiresIn: '24h' });
};
// Register new user
const register = async (req, res) => {
    try {
        const { username, email, password, role, is_active = true } = req.body;
        // Check if user already exists
        const existingUsers = await (0, database_1.executeQuery)('SELECT id FROM users WHERE username = ? OR email = ?', [username, email]);
        if (existingUsers && existingUsers.length > 0) {
            res.status(409).json({ error: 'Username or email already exists' });
            return;
        }
        // Hash password
        const saltRounds = 12;
        const password_hash = await bcryptjs_1.default.hash(password, saltRounds);
        // Insert new user
        const result = await (0, database_1.executeQuery)(`INSERT INTO users (username, email, password_hash, role, is_active) 
       VALUES (?, ?, ?, ?, ?)`, [username, email, password_hash, role, is_active]);
        // Get the created user
        const newUsers = await (0, database_1.executeQuery)('SELECT id, username, email, role, is_active, created_at, updated_at FROM users WHERE id = ?', [result.insertId]);
        const newUser = newUsers[0];
        res.status(201).json({
            message: 'User registered successfully',
            user: newUser
        });
    }
    catch (error) {
        console.error('Registration error:', error);
        res.status(500).json({ error: 'Registration failed' });
    }
};
exports.register = register;
// Login user
const login = async (req, res) => {
    try {
        const { email, password } = req.body;
        // Get user by email
        const users = await (0, database_1.executeQuery)('SELECT * FROM users WHERE email = ? AND is_active = 1', [email]);
        if (!users || users.length === 0) {
            res.status(401).json({ error: 'Invalid credentials' });
            return;
        }
        const user = users[0];
        // Verify password
        const isValidPassword = await bcryptjs_1.default.compare(password, user.password_hash);
        if (!isValidPassword) {
            res.status(401).json({ error: 'Invalid credentials' });
            return;
        }
        // Update last login
        await (0, database_1.executeQuery)('UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?', [user.id]);
        // Generate token
        const token = generateToken(user);
        // Remove password from response
        const { password_hash, ...userWithoutPassword } = user;
        const response = {
            user: userWithoutPassword,
            token,
            expires_in: process.env.JWT_EXPIRES_IN || '24h'
        };
        res.json({
            message: 'Login successful',
            ...response
        });
    }
    catch (error) {
        console.error('Login error:', error);
        res.status(500).json({ error: 'Login failed' });
    }
};
exports.login = login;
// Get current user profile
const getProfile = async (req, res) => {
    try {
        if (!req.user) {
            res.status(401).json({ error: 'Authentication required' });
            return;
        }
        // Get user with employee details if exists
        const profiles = await (0, database_1.executeQuery)(`
      SELECT 
        u.id, u.username, u.email, u.role, u.is_active, u.last_login, u.created_at, u.updated_at,
        e.id as employee_id, e.employee_id as emp_id, e.first_name, e.last_name, e.position,
        d.name as department_name
      FROM users u
      LEFT JOIN employees e ON u.id = e.user_id
      LEFT JOIN departments d ON e.department_id = d.id
      WHERE u.id = ?
    `, [req.user.id]);
        if (!profiles || profiles.length === 0) {
            res.status(404).json({ error: 'User profile not found' });
            return;
        }
        const profileData = profiles[0];
        const profile = {
            id: profileData.id,
            username: profileData.username,
            email: profileData.email,
            role: profileData.role,
            is_active: profileData.is_active,
            last_login: profileData.last_login,
            created_at: profileData.created_at,
            updated_at: profileData.updated_at
        };
        // Add employee data if exists
        if (profileData.employee_id) {
            profile.employee = {
                id: profileData.employee_id,
                employee_id: profileData.emp_id,
                first_name: profileData.first_name,
                last_name: profileData.last_name,
                position: profileData.position,
                department_name: profileData.department_name
            };
        }
        res.json({ profile });
    }
    catch (error) {
        console.error('Get profile error:', error);
        res.status(500).json({ error: 'Failed to get profile' });
    }
};
exports.getProfile = getProfile;
// Update user profile
const updateProfile = async (req, res) => {
    try {
        if (!req.user) {
            res.status(401).json({ error: 'Authentication required' });
            return;
        }
        const { username, email, password } = req.body;
        const updates = [];
        const values = [];
        if (username) {
            // Check if username is already taken by another user
            const existingUsers = await (0, database_1.executeQuery)('SELECT id FROM users WHERE username = ? AND id != ?', [username, req.user.id]);
            if (existingUsers && existingUsers.length > 0) {
                res.status(409).json({ error: 'Username already exists' });
                return;
            }
            updates.push('username = ?');
            values.push(username);
        }
        if (email) {
            // Check if email is already taken by another user
            const existingUsers = await (0, database_1.executeQuery)('SELECT id FROM users WHERE email = ? AND id != ?', [email, req.user.id]);
            if (existingUsers && existingUsers.length > 0) {
                res.status(409).json({ error: 'Email already exists' });
                return;
            }
            updates.push('email = ?');
            values.push(email);
        }
        if (password) {
            const saltRounds = 12;
            const password_hash = await bcryptjs_1.default.hash(password, saltRounds);
            updates.push('password_hash = ?');
            values.push(password_hash);
        }
        if (updates.length === 0) {
            res.status(400).json({ error: 'No valid fields to update' });
            return;
        }
        updates.push('updated_at = CURRENT_TIMESTAMP');
        values.push(req.user.id);
        await (0, database_1.executeQuery)(`UPDATE users SET ${updates.join(', ')} WHERE id = ?`, values);
        res.json({ message: 'Profile updated successfully' });
    }
    catch (error) {
        console.error('Update profile error:', error);
        res.status(500).json({ error: 'Failed to update profile' });
    }
};
exports.updateProfile = updateProfile;
// Logout (client-side token invalidation)
const logout = async (req, res) => {
    res.json({ message: 'Logout successful. Please remove the token from client storage.' });
};
exports.logout = logout;
// Refresh token
const refreshToken = async (req, res) => {
    try {
        if (!req.user) {
            res.status(401).json({ error: 'Authentication required' });
            return;
        }
        // Generate new token
        const token = generateToken(req.user);
        res.json({
            message: 'Token refreshed successfully',
            token,
            expires_in: process.env.JWT_EXPIRES_IN || '24h'
        });
    }
    catch (error) {
        console.error('Refresh token error:', error);
        res.status(500).json({ error: 'Failed to refresh token' });
    }
};
exports.refreshToken = refreshToken;
//# sourceMappingURL=authController.js.map