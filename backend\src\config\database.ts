import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'hospital_employee_management',
  waitForConnections: true,
  namedPlaceholders: false,
  connectionLimit: 10,
  queueLimit: 0,
};

// Create connection pool
const pool = mysql.createPool(dbConfig);

// Test database connection
export const testConnection = async (): Promise<void> => {
  try {
    const connection = await pool.getConnection();
    console.log('✅ Database connected successfully');
    connection.release();
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    throw error;
  }
};

// Execute query with error handling
export const executeQuery = async (query: string, params?: any[]): Promise<any> => {
  try {
    // Ensure params is always an array and filter out undefined values
    const cleanParams = params ? params.filter(p => p !== undefined) : [];
    const [results] = await pool.query(query, cleanParams);
    return results;
  } catch (error) {
    console.error('Database query error:', error);
    console.error('Query:', query);
    console.error('Parameters:', params);
    throw error;
  }
};

// Get connection from pool
export const getConnection = async () => {
  return await pool.getConnection();
};

// Database initialization function
export const initializeDatabase = async (): Promise<void> => {
  try {
    await testConnection();
    console.log('🗄️  Database initialization completed');
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    process.exit(1);
  }
};

// Helper function to format query results
export const formatQueryResult = (result: any): any => {
  if (Array.isArray(result)) {
    return result.map(row => {
      // Convert MySQL datetime to ISO string
      Object.keys(row).forEach(key => {
        if (row[key] instanceof Date) {
          row[key] = row[key].toISOString();
        }
      });
      return row;
    });
  }
  return result;
};

// Transaction helper
export const executeTransaction = async (queries: Array<{query: string, params?: any[]}>): Promise<any[]> => {
  const connection = await getConnection();
  try {
    await connection.beginTransaction();

    const results = [];
    for (const {query, params} of queries) {
      const [result] = await connection.execute(query, params);
      results.push(result);
    }

    await connection.commit();
    return results;
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
};

export default pool;
